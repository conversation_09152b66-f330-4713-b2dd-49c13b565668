package ma.almobadara.backend.mapper;

import ma.almobadara.backend.dto.administration.CaisseEmpruntHistoryDto;
import ma.almobadara.backend.model.administration.CaisseEmpruntHistory;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface CaisseEmpruntHistoryMapper {
    
    @Mapping(target = "caisseEmpruntId", source = "caisseEmprunt.id")
    CaisseEmpruntHistoryDto toDto(CaisseEmpruntHistory caisseEmpruntHistory);
    
    @Mapping(target = "caisseEmprunt", ignore = true) // Will be set separately in service
    CaisseEmpruntHistory toEntity(CaisseEmpruntHistoryDto caisseEmpruntHistoryDto);
}
